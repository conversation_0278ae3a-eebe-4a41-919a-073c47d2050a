# lin_trans 项目概览

## 项目目的
lin_trans 是一个音视频转字幕和字幕翻译的桌面应用程序。主要功能包括：
- 音视频转字幕（Video2SRT）
- 字幕翻译（WorkSrt）
- 个人创作管理（TableApp）
- 用户认证和个人中心功能

## 技术栈
- **前端框架**: PySide6 (Qt for Python 6.7.2) 
- **UI组件库**: qfluentwidgets (Fluent Design风格)
- **异步处理**: asyncio, httpx
- **音频处理**: funasr, torch, torchaudio
- **云服务**: 阿里云ASR, 阿里云OSS
- **数据库**: SQLAlchemy
- **AI/ML**: OpenAI, dashscope (通义千问)
- **打包工具**: PyInstaller, Nuitka

## 项目结构
- `nice_ui/` - 主要UI模块
- `agent/` - AI翻译代理模块
- `app/` - 核心应用逻辑
- `components/` - UI组件和资源
- `vendor/qfluentwidgets/` - 第三方UI库
- `utils/` - 工具函数
- `orm/` - 数据库模型