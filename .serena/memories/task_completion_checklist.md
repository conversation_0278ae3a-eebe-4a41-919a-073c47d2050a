# 任务完成检查清单

## 代码开发完成后应该执行的步骤

### 1. 代码质量检查
- [ ] 检查代码是否遵循项目命名约定
- [ ] 确保新代码有适当的错误处理
- [ ] 检查是否有未使用的导入
- [ ] 确保日志记录合适（使用loguru）

### 2. 功能测试
- [ ] 手动测试新功能是否正常工作
- [ ] 测试UI响应是否正常
- [ ] 检查是否影响现有功能

### 3. 资源文件更新
- [ ] 如果添加了新的图标或资源，更新 `lin_resource.qrc`
- [ ] 运行 `pyside6-rcc components/lin_resource.qrc -o components/lin_resource_rc.py`
- [ ] 在 `resource_manager.py` 中添加相应映射

### 4. 配置和设置
- [ ] 如果有新的配置项，添加到配置管理中
- [ ] 确保设置能正确保存和加载

### 5. 构建测试
- [ ] 运行 `python run.py` 确保应用正常启动
- [ ] 如果是重要更新，测试打包构建

### 6. 文档更新
- [ ] 更新相关的README或文档
- [ ] 如果有新的API或配置，添加说明

## 注意事项
- 目前项目没有自动化测试，建议手动测试
- 注意PySide6版本必须是6.7.2，不要升级到6.8.0
- 确保多线程操作正确使用信号槽机制
- 云服务相关功能需要有网络连接才能测试