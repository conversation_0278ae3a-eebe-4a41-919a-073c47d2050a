# lin_trans 项目建议命令

## 开发环境命令 (Windows)
- `dir` - 查看目录内容
- `cd <directory>` - 切换目录
- `python -V` - 查看Python版本
- `pip list` - 查看已安装包

## 项目运行命令
- `python run.py` - 启动主应用
- `python start.py` - 备用启动脚本
- `python -m nice_ui.ui.MainWindow` - 直接运行主窗口

## 打包构建命令
- `python build.py` - 完整构建应用
- `python incremental_build.py` - 增量构建
- `python install_build.py` - 安装后构建

## 资源管理命令
- `pyside6-rcc components/lin_resource.qrc -o components/lin_resource_rc.py` - 生成Qt资源文件

## 依赖管理命令
- `pip install -r requirements.txt` - 安装依赖
- `pip download --prefer-binary --dest wheels -r requirements.txt` - 下载离线包
- `pip install --no-index --find-links wheels -r requirements.txt` - 离线安装

## 代码质量工具 (如果有的话)
- 目前项目未发现明确的linting/formatting配置
- 建议使用 `black`, `flake8` 或 `ruff` 进行代码格式化

## 测试命令
- `python -m pytest test/` - 运行测试（如果有测试的话）

## 进程管理 (Linux/macOS风格，需要适配Windows)
- `tasklist | findstr python` - 查看Python进程 (Windows)
- `taskkill /f /im python.exe` - 结束Python进程 (Windows)