# lin_trans 代码风格和约定

## 命名约定
- **类名**: PascalCase (如 `Window`, `Video2SRT`, `WorkSrt`)
- **函数名**: snake_case (如 `get_user_balance`, `handle_login_success`)
- **变量名**: snake_case (如 `user_info`, `settings_manager`)
- **常量**: UPPER_SNAKE_CASE (如 `MAIN_WINDOW_SIZE`, `DEFAULT_PROGRESS_RANGE`)
- **私有成员**: 下划线前缀 (如 `_token`, `_refresh_token`)

## 文件和目录结构
- **包目录**: snake_case (如 `nice_ui`, `cloud_asr`)
- **模块文件**: snake_case (如 `main_worker.py`, `setting_ui.py`)
- **UI相关文件**: 通常以功能命名 (如 `MainWindow.py`, `login.py`)

## 类型提示
- 项目中部分使用了类型提示，建议在新代码中使用
- 使用 `typing` 模块的类型 (如 `Optional`, `Dict`, `List`)

## 文档字符串
- 大部分代码缺少docstring，建议添加
- 建议使用Google风格的docstring

## 导入规范
- 标准库导入在最前面
- 第三方库导入在中间
- 本地模块导入在最后
- 使用相对导入和绝对导入混合的方式

## UI组件约定
- 继承自qfluentwidgets的组件
- 使用信号槽机制进行组件间通信
- UI和业务逻辑分离

## 配置管理
- 使用QSettings进行配置存储
- 配置项通过 `nice_ui.configure.config` 模块管理
- 支持多语言配置