# lin_trans 应用架构

## 整体架构
lin_trans 采用分层架构设计，主要分为以下层次：

### 1. 表示层 (UI Layer)
- **主窗口**: `nice_ui/ui/MainWindow.py` - 应用主入口
- **功能页面**: `Video2SRT`, `WorkSrt`, `TableApp` - 核心功能界面
- **设置页面**: `SettingInterface` - 应用配置界面
- **登录系统**: `LoginWindow`, `ProfileInterface` - 用户认证

### 2. 服务层 (Service Layer)
- **认证服务**: `AuthService` - 用户登录认证
- **代币服务**: `TokenService` - 代币管理和消耗计算
- **API客户端**: `api_client.py` - 与后端API交互
- **任务管理**: `ASRTaskManager`, `TransTaskManager` - 任务调度

### 3. 业务逻辑层 (Business Layer)
- **音频处理**: `app/listen.py` - 本地ASR功能
- **视频工具**: `app/video_tools.py` - 视频处理工具
- **翻译代理**: `agent/` - AI翻译功能
- **云服务**: `app/cloud_asr/`, `app/cloud_trans/` - 云端服务

### 4. 数据层 (Data Layer)
- **ORM模型**: `orm/` - 数据库模型定义
- **配置管理**: `nice_ui/configure/` - 应用配置
- **文件工具**: `utils/file_utils.py` - 文件操作

## 设计模式

### 1. 依赖注入
使用 `ServiceProvider` 管理服务实例，实现松耦合

### 2. 观察者模式
大量使用Qt的信号槽机制进行组件间通信

### 3. 单例模式
配置管理器、服务提供者等使用单例模式

### 4. 工厂模式
任务处理器通过工厂模式创建

## 核心组件交互

### 启动流程
1. `run.py` / `start.py` 启动应用
2. 创建 `MainWindow` 实例
3. 初始化服务提供者和依赖注入
4. 尝试自动登录
5. 加载UI界面和配置

### 任务执行流程
1. 用户在UI提交任务
2. 任务进入队列 (`LinQueue`)
3. 任务处理器处理具体逻辑
4. 通过信号更新UI状态
5. 结果保存到数据库或文件

## 并发处理
- 使用Qt线程 (`QThread`) 处理耗时操作
- 异步任务通过 `asyncio` 和 `httpx` 处理
- 队列消费者模式处理批量任务