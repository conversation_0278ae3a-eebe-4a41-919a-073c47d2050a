#!/usr/bin/env python3
"""
Nuitka 构建脚本
使用 Nuitka 编译项目，仅编译项目文件，第三方依赖不进行编译
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def clean_build_dir():
    """清理构建目录"""
    build_dirs = ["dist", "build", "*.build", "*.dist"]
    for pattern in build_dirs:
        if "*" in pattern:
            # 处理通配符模式
            for path in Path(".").glob(pattern):
                if path.is_dir():
                    print(f"删除构建目录: {path}")
                    shutil.rmtree(path, ignore_errors=True)
        else:
            path = Path(pattern)
            if path.exists():
                print(f"删除构建目录: {path}")
                if path.is_dir():
                    shutil.rmtree(path, ignore_errors=True)
                else:
                    path.unlink()


def build_with_nuitka(debug_mode=False):
    """使用 Nuitka 构建项目"""

    # 根据调试模式选择控制台模式
    console_mode = "attach" if debug_mode else "disable"
    output_filename = "LappedAI_Debug.exe" if debug_mode else "LappedAI.exe"

    # 构建命令
    cmd = [
        sys.executable, "-m", "nuitka",
        
        # 基本配置
        "--main=run.py",
        f"--output-filename={output_filename}",
        "--output-dir=dist",
        
        # 编译模式
        "--standalone",
        "--assume-yes-for-downloads",
        
        # 第三方依赖不编译但要包含
        # 注意：有插件支持的包（PySide6, numpy）由插件处理
        # torch 由于编译问题，作为外部依赖处理
        "--nofollow-import-to=torch",
        "--include-package-data=torch",
        "--nofollow-import-to=scipy",
        "--include-package-data=scipy",
        "--nofollow-import-to=torchaudio",
        "--include-package-data=torchaudio",
        "--nofollow-import-to=funasr",
        "--include-package-data=funasr",
        "--nofollow-import-to=modelscope",
        "--include-package-data=modelscope",
        "--nofollow-import-to=httpx",
        "--include-package-data=httpx",
        "--nofollow-import-to=loguru",
        "--include-package-data=loguru",
        "--nofollow-import-to=openai",
        "--include-package-data=openai",
        "--nofollow-import-to=sqlalchemy",
        "--include-package-data=sqlalchemy",
        "--nofollow-import-to=socksio",
        "--include-package-data=socksio",
        "--nofollow-import-to=packaging",
        "--include-package-data=packaging",
        "--nofollow-import-to=darkdetect",
        "--include-package-data=darkdetect",
        "--nofollow-import-to=av",
        "--include-package-data=av",
        "--nofollow-import-to=alibabacloud-oss-v2",
        "--include-package-data=alibabacloud-oss-v2",
        "--nofollow-import-to=dashscope",
        "--include-package-data=dashscope",
        "--nofollow-import-to=pydantic",
        "--include-package-data=pydantic",
        "--nofollow-import-to=pydantic_settings",
        "--include-package-data=pydantic_settings",
        "--nofollow-import-to=dotenv",
        "--include-package-data=dotenv",
        "--nofollow-import-to=pytz",
        "--include-package-data=pytz",

        # 单文件模块（不需要 --include-package-data）
        "--nofollow-import-to=colorthief",
        "--nofollow-import-to=path",
        
        # 包含项目模块进行编译
        "--follow-import-to=nice_ui",
        "--follow-import-to=app",
        "--follow-import-to=agent", 
        "--follow-import-to=components",
        "--follow-import-to=orm",
        "--follow-import-to=services",
        "--follow-import-to=tools",
        "--follow-import-to=utils",
        "--follow-import-to=vendor",
        "--follow-import-to=videotrans",
        
        # 包含数据文件和目录
        "--include-data-dir=components/assets=components/assets",
        "--include-data-dir=components/themes=components/themes",
        "--include-data-dir=nice_ui/language=nice_ui/language", 
        "--include-data-dir=config=config",
        
        # 包含单个数据文件
        "--include-data-files=videotrans/set.ini=videotrans/set.ini",
        "--include-data-files=videotrans/azure.txt=videotrans/azure.txt",
        "--include-data-files=videotrans/chatgpt.txt=videotrans/chatgpt.txt",
        "--include-data-files=videotrans/gemini.txt=videotrans/gemini.txt",
        "--include-data-files=videotrans/localllm.txt=videotrans/localllm.txt",
        "--include-data-files=videotrans/zijie.txt=videotrans/zijie.txt",
        
        # Windows 特定配置
        f"--windows-console-mode={console_mode}",
        "--windows-icon-from-ico=components/assets/lapped.ico",
        
        # 优化选项
        "--show-progress",
        "--show-memory",
        
        # 插件配置
        "--enable-plugin=pyside6",
        "--enable-plugin=numpy",
        # torch 插件暂时禁用，因为编译时出现问题

        # 禁用对有问题的 torch 模块的跟踪，避免编译错误
        "--nofollow-import-to=torch.fx",
        "--nofollow-import-to=torch.fx.experimental",
        "--nofollow-import-to=torch.fx.experimental._config",
        "--nofollow-import-to=torch.jit",
        "--nofollow-import-to=torch.distributed",

        # 确保包含必要的模块
        "--include-module=sysconfig",
        "--include-module=site",
        "--include-module=encodings",
        "--include-module=codecs",

        # 排除一些可能导致问题的 torch 子模块
        "--nofollow-import-to=torch.testing",
        "--nofollow-import-to=torch.utils.benchmark",
        "--nofollow-import-to=torch.profiler",

        # 包含隐式导入
        "--include-package=pkg_resources",
        "--include-package=setuptools",

        # 禁用一些不需要的功能
        "--no-deployment-flag=self-execution",

        # 输出详细信息
        "--verbose"
    ]
    
    print("开始使用 Nuitka 构建项目...")
    print(f"构建命令: {' '.join(cmd)}")
    
    try:
        # 执行构建命令
        result = subprocess.run(cmd, check=True, text=True)
        print("✅ 构建成功完成!")
        
        # 显示输出文件信息
        dist_dir = Path("dist")
        if dist_dir.exists():
            print(f"\n📁 构建输出目录: {dist_dir.absolute()}")
            for item in dist_dir.iterdir():
                if item.is_file():
                    size = item.stat().st_size / (1024 * 1024)  # MB
                    print(f"   📄 {item.name} ({size:.1f} MB)")
                elif item.is_dir():
                    print(f"   📁 {item.name}/")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 错误: 找不到 Nuitka。请确保已安装 Nuitka:")
        print("   pip install nuitka")
        return False


def check_dependencies():
    """检查必要的依赖"""
    print("🔍 检查构建依赖...")

    # 检查 Nuitka
    try:
        import nuitka
        print(f"✅ Nuitka 版本: {nuitka.__version__}")
    except ImportError:
        print("❌ 错误: 未找到 Nuitka，请安装:")
        print("   pip install nuitka")
        return False

    # 检查关键依赖
    critical_deps = ["PySide6", "numpy", "torch"]
    for dep in critical_deps:
        try:
            __import__(dep)
            print(f"✅ {dep} 已安装")
        except ImportError:
            print(f"❌ 错误: 未找到 {dep}，请安装:")
            print(f"   pip install {dep}")
            return False

    return True


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Nuitka 项目构建工具")
    parser.add_argument("--debug", action="store_true",
                       help="构建调试版本（显示控制台输出）")
    args = parser.parse_args()

    print("🚀 Nuitka 项目构建工具")
    print("=" * 50)

    if args.debug:
        print("🐛 调试模式：将构建带控制台输出的版本")
    else:
        print("🚀 发布模式：将构建无控制台的版本")

    # 检查是否在项目根目录
    if not Path("run.py").exists():
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)

    # 清理构建目录
    print("\n🧹 清理旧的构建文件...")
    clean_build_dir()

    # 开始构建
    print("\n🔨 开始构建...")
    success = build_with_nuitka(debug_mode=args.debug)

    if success:
        print("\n🎉 构建完成!")
        exe_name = "LappedAI_Debug.exe" if args.debug else "LappedAI.exe"
        print(f"可执行文件位于: dist/{exe_name}")
        print("\n📋 使用说明:")
        print("1. 将 dist 目录复制到目标机器")
        print(f"2. 运行 {exe_name} 启动程序")
        if args.debug:
            print("3. 调试版本会显示控制台输出，便于调试")
            print("4. 从命令行运行可以看到所有打印信息")
        else:
            print("3. 发布版本不显示控制台窗口")
        print("4. 确保目标机器已安装 Visual C++ Redistributable")
    else:
        print("\n💥 构建失败!")
        print("请检查上面的错误信息并修复问题后重试")
        sys.exit(1)


if __name__ == "__main__":
    main()
