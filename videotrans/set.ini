;####################
;如果你不确定修改后将会带来什么影响，请勿随意修改，修改前请做好备份， 如果出问题请恢复
;升级前请做好备份，升级后按照原备份重新修改。请勿直接用备份文件覆盖，因为新版本可能有新增配置
;If you are not sure what effect the modification will bring, please don't modify it arbitrarily, please make a good backup before modification, and restore it if something goes wrong.
;Please make a backup before upgrading, and then modify it according to the original backup after upgrading. Please do not overwrite the backup file directly, because the new version may have new configurations.

;##############界面语言文字#############################
;Interface language text #############################
;默认界面跟随系统，也可以在此手动指定，zh=中文界面，en=英文界面
;Default interface follows the system, you can also specify it manually here, zh=Chinese interface, en=English interface.
lang =

;##################视频质量############################
;Video quality ############################
;视频处理质量，0-51的整数，0=无损处理尺寸较大速度很慢，51=质量最低尺寸最小处理速度最快
;Video processing quality, integer 0-51, 0=lossless processing with large size is very slow, 51=lowest quality with smallest size is fastest processing speed
crf=13
; 采用 libx264 编码或 libx265编码，264兼容性更好，265压缩比更大清晰度更高
video_codec=264

;#################模型名字列表#################################
;List of model names #################################
;可供选择的chatGPT模型，以英文逗号分隔
;Available chatGPT models, separated by English commas
chatgpt_model=gpt-3.5-turbo,gpt-4,gpt-4-turbo-preview,qwen,moonshot-v1-8k,deepseek-chat
localllm_model=qwen,moonshot-v1-8k,deepseek-chat,gpt-3.5-turbo


;################声画字幕对齐相关#################################
;Sound and picture subtitle alignment related #################################

;音频最大加速倍数，默认1.5，即最大加速到 1.5倍速度，需设置1-100的数字，比如1.5，代表最大加速1.5倍
;Maximum audio acceleration, default 1.5, that is, the maximum acceleration to 1.5 times the speed, need to set the number of 1-100, such as 1.5, represents the maximum acceleration 1.5 times
audio_rate=1.5

; 设为大于1的数，代表最大允许慢速多少倍，0或1代表不进行视频慢放
; set to a number greater than 1, representing the maximum number of times allowed to slow down, 0 or 1 represents no video slowdown
video_rate=20

;是否移除配音末尾空白，true=移除，false=不移除
;Whether to remove voiceover end blanks, true=remove, false=don't remove
remove_silence=true

;是否移除原始字幕时长大于配音时长 的静音，比如原时长5s，配音后3s，是否移除这2s静音，true=移除，false=不移除
;If or not remove the silence when the original duration of subtitle is longer than the dubbing duration, for example, if the original duration is 5s and the dubbing duration is 3s, if or not remove the 2s of silence, true=remove, false=don't remove.
remove_srt_silence=false

;移除2条字幕间的静音长度ms，比如100ms，即如果两条字幕间的间隔大于100ms时，将移除100ms
; Remove the mute length of ms between 2 subtitles, e.g. 100ms, i.e. if the interval between two subtitles is greater than 100ms, 100ms will be removed
remove_white_ms=0


;true=强制修改字幕时间轴以便匹配声音，false=不修改，保持原始字幕时间轴，不修改可能导致字幕和声音不匹配
;true=Forces the subtitle timeline to be modified in order to match the sound, false=Does not modify it, keeps the original subtitle timeline, not modifying it may result in a mismatch between the subtitle and the sound
force_edit_srt=true

; ###############语句分割相关##################################
; statement segmentation related ##################################

;faster-whisper字幕整体识别模式时启用自定义静音分割片段，true=启用，显存不足时，可以设为false禁用
;Enable custom mute segmentation when subtitles are in overall recognition mode, true=enable, can be set to false to disable when video memory is insufficient.
vad=true

;用于 faster-whisper 时 VAD选项设置作为切割依据的最小静音片段ms，默认250ms 以及最大句子时长6s
;The minimum silent segmentation ms, default 200ms, and the maximum sentence length 3s are used for pre-segmentation and overall recognition as the basis for segmentation.
overall_silence=250
overall_maxsecs=6
overall_threshold=0.5
overall_speech_pad_ms=100


;用于均等分割时和openai模式时，作为切割依据的最小静音片段ms，默认200ms，即只有大于等于200ms的静音处才分割
; used for equal segmentation, as the basis for cutting the minimum silence segment ms, the default 200ms, that is, only greater than or equal to 200ms silence at the split
voice_silence=250
;用于均等分割时的每个切片时长 秒，默认 10s,即每个字幕时长大约都是10s
;seconds per slice for equalization, default 6s, i.e. each subtitle is about 6s.
interval_split=10


;################翻译配音速度#############################
;Translation dubbing speed #############################

;同时翻译的数量，1-20，不要太大，否则可能触发翻译api频率限制
;Translation dubbing speed #############################
trans_thread=15

;翻译出错重试次数
;Number of retries for translation errors
retries=2

;同时配音的数量，1-10，建议不要大于5，否则容易失败
; The number of simultaneous voiceovers, 1-10, it is recommended not to be greater than 5, otherwise it will be easy to fail
dubbing_thread=5


;字幕识别完成等待翻译前的暂停秒数，和翻译完等待配音的暂停秒数
; seconds of pause before subtitle recognition is completed and waiting for translation, and seconds of pause after translation and waiting for dubbing.
countdown_sec=15


;#####################背景声音########################################
;Background sound ########################################

;背景声音音量降低或升高幅度，大于1升高，小于1降低
; Background sound volume is lowered or raised, greater than 1 raised, less than 1 lowered
backaudio_volume=0.5

;背景音分离时切分片段，太长的音频会耗尽显存，因此切分后分离，单位s,默认 600s
;Background sound is separated by a slice, if the audio is too long, it will exhaust the memory, so it is separated by a slice, the unit is s, default is 600s.
separate_sec=600

; 如果背景音频时长短于视频，是否重复播放背景音，默认否
;Background sound is separated by a slice, if the audio is too long, it will exhaust the memory, so it is separated by a slice, the unit is s, default is 600s.
loop_backaudio=false


;####################GPU FFmpeg #####################################

; ##################字幕识别-GPU提高降低性能相关############################################
;Subtitle Recognition - GPU Improvement Reduced Performance Related
;从视频中识别字幕时的cuda数据类型，int8=消耗资源少，速度快，精度低，float32=消耗资源多，速度慢，精度高，int8_float16=设备自选
; cuda data type when recognizing subtitles from video, int8=consumes fewer resources, faster, lower precision, float32=consumes more resources, slower, higher precision, int8_float16=device of choice
cuda_com_type=float32

;发送给whisper模型的提示词
;Cue words sent to the whisper model.
initial_prompt_zh=Please break sentences correctly and retain punctuation.

;字幕识别时，cpu进程
;cpu process during subtitle recognition
whisper_threads=4

;字幕识别时，同时工作进程
; Simultaneous work processes during subtitle recognition
whisper_worker=1

;字幕识别时精度调整，1-5，1=消耗资源最低，5=消耗最多，如果显存充足，可以设为5，可能会取得更精确的识别结果
;Subtitle recognition accuracy adjustment, 1-5, 1 = consume the lowest resources, 5 = consume the most, if the video memory is sufficient, you can set it to 5, you may get more accurate recognition results.
beam_size=5
best_of=5



;0=占用更少GPU资源但效果略差，1=占用更多GPU资源同时效果更好
;0 = less GPU resources but slightly worse results, 1 = more GPU resources and better results at the same time
temperature=0

;同 temperature, true=占用更多GPU效果更好，false=占用更少GPU效果略差
; same as temperature, true=better with more GPUs, false=slightly worse with fewer GPUs
condition_on_previous_text=false

model_list=tiny,tiny.en,base,base.en,small,small.en,medium,medium.en,large-v1,large-v2,large-v3,distil-whisper-small.en,distil-whisper-medium.en,distil-whisper-large-v2,distil-whisper-large-v3



; ###################字幕设置相关 Subtitle Settings######################################

;硬字幕时可在这里设置字幕字体大小，填写整数数字，比如12，代表字体12px大小，20代表20px大小，0等于默认大小
;Hard subtitles can be set here when the subtitle font size, fill in the integer numbers, such as 12, on behalf of the font size of 12px, 20 on behalf of the size of 20px, 0 is equal to the default size
fontsize=16

;硬字幕时要使用的字体名字，注意不是字体文件名称，而是显示名称，比如 黑体 宋体 等
;如果不清楚字体名称，可以创建个word文档，选择要使用的字体，查看名字
;必须确保正确填写了字体名称，否则字幕可能无法显示或者无法使用指定的字体显示
fontname=黑体

;设置字体的颜色，注意&H后的6个字符，每2个字母分别代表 BGR 颜色，即2位蓝色/2位绿色/2位红色，同同时常见的RGB色色颠倒的。
;比如 白色=&HFFFFFF, 黑色=&H000000  蓝色=&HFF0000  绿色=&H00FF00  红色=&H0000FF
fontcolor=&HFFFFFF
;字体边框颜色，规则同上
fontbordercolor=&H000000

;字幕默认位于视频底部，此处可设置大于0的数值，代表字幕上移多少距离，注意最大不可大于(视频高度-20),也就是要保留至少20的高度用于显示字幕，否则字幕将不可见
subtitle_bottom=10



;中日韩字幕一行长度字符个数，多于这个将换行
;CJK subtitle line length character count, more than this will be line feeds.
cjk_len=30

;其他语言换行长度，多于这个字符数量将换行
;Other language line breaks, more than this number of characters will be a line break.
other_len=60

;用于兼容ffmpeg，如果出现ffmpeg报错，错误中含有 vysnc字样，可改为 vsync=vfr
; used for ffmpeg compatibility, if ffmpeg error, the error contains the word vysnc, can be changed to vsync=vfr
vsync=0


;true=批量任务时分为 识别、翻译、配音、合并 多阶段交叉执行，加快速度但不可暂停编辑字幕，false=前面全部完成后才开始下一个，可暂停编辑字幕
;true=The batch task is divided into recognition, translation, dubbing, merging, and multi-stage cross-execution to accelerate the speed, false=The next one starts after all the previous ones are completed.
cors_run=true



;chatTTS音色值
chattts_voice=11,12,16,2222,4444,6653,7869,9999,5,13,14,1111,3333,4099,5099,5555,8888,6666,7777