# Nuitka 构建说明

本项目使用 Nuitka 进行打包，仅编译项目文件，第三方依赖不进行编译但会包含在打包程序中。

## 构建要求

### 系统要求
- Windows 10/11 (推荐)
- Python 3.11 或 3.12
- Visual Studio Build Tools 或 Visual Studio Community

### 必要依赖
```bash
pip install nuitka>=2.4.8
```

### 项目依赖
确保已安装项目的所有依赖：
```bash
pip install -r requirements.txt
```

## 构建方法

### 方法1: 使用构建脚本（推荐）
```bash
# Windows
build.bat

# 或直接运行 Python 脚本
python build_nuitka.py
```

### 方法2: 使用 pyproject.toml 配置
```bash
python -m nuitka --config-file=pyproject.toml run.py
```

### 方法3: 手动命令行
```bash
python -m nuitka \
    --main=run.py \
    --output-filename=LappedAI.exe \
    --output-dir=dist \
    --standalone \
    --assume-yes-for-downloads \
    --windows-console-mode=disable \
    --windows-icon-from-ico=components/assets/lapped.ico \
    --enable-plugin=pyside6 \
    --enable-plugin=numpy \
    --enable-plugin=torch \
    --follow-import-to=nice_ui \
    --follow-import-to=app \
    --follow-import-to=agent \
    --follow-import-to=components \
    --follow-import-to=orm \
    --follow-import-to=services \
    --follow-import-to=tools \
    --follow-import-to=utils \
    --follow-import-to=vendor \
    --follow-import-to=videotrans \
    --nofollow-import-to=numpy \
    --include-package-data=numpy \
    --nofollow-import-to=torch \
    --include-package-data=torch \
    --nofollow-import-to=PySide6 \
    --include-package-data=PySide6 \
    --include-data-dir=components/assets=components/assets \
    --include-data-dir=components/themes=components/themes \
    --include-data-dir=nice_ui/language=nice_ui/language \
    --include-data-dir=config=config \
    --show-progress \
    --verbose
```

## 构建配置说明

### 编译策略
- **项目模块**: 完全编译为 C++ 代码，提高性能和保护源码
- **第三方依赖**: 不编译但包含在打包程序中，确保兼容性

### 包含的项目模块
- `nice_ui` - 主要 UI 模块
- `app` - 应用核心逻辑
- `agent` - AI 代理模块
- `components` - UI 组件
- `orm` - 数据库操作
- `services` - 服务层
- `tools` - 工具函数
- `utils` - 实用工具
- `vendor` - 第三方组件
- `videotrans` - 视频处理

### 不编译的第三方依赖
- `numpy`, `scipy` - 数值计算
- `torch`, `torchaudio` - 深度学习框架
- `PySide6` - GUI 框架
- `funasr` - 语音识别
- `modelscope` - 模型库
- `httpx` - HTTP 客户端
- `loguru` - 日志库
- `openai` - OpenAI API
- `sqlalchemy` - 数据库 ORM
- 其他依赖...

## 构建输出

构建成功后，会在 `dist` 目录下生成：
```
dist/
├── LappedAI.exe          # 主程序
├── _internal/            # 依赖文件
│   ├── PySide6/         # Qt 库
│   ├── torch/           # PyTorch 库
│   ├── numpy/           # NumPy 库
│   └── ...              # 其他依赖
├── components/          # 资源文件
├── config/              # 配置文件
└── ...                  # 其他数据文件
```

## 部署说明

1. 将整个 `dist` 目录复制到目标机器
2. 确保目标机器已安装 Visual C++ Redistributable
3. 运行 `LappedAI.exe` 启动程序

## 故障排除

### 常见问题

1. **构建失败: 找不到 Visual Studio**
   ```
   解决方案: 安装 Visual Studio Build Tools 或 Visual Studio Community
   ```

2. **运行时错误: 找不到模块**
   ```
   解决方案: 检查 --include-package-data 配置是否包含了所需模块
   ```

3. **程序启动慢**
   ```
   原因: 第三方依赖未编译，首次加载需要时间
   解决方案: 这是正常现象，后续启动会更快
   ```

4. **缺少资源文件**
   ```
   解决方案: 检查 --include-data-dir 和 --include-data-files 配置
   ```

### 调试技巧

1. 使用 `--verbose` 查看详细构建信息
2. 使用 `--show-progress` 显示构建进度
3. 检查构建日志中的警告信息
4. 在开发环境中测试所有功能

## 性能优化

1. **编译优化**: 项目代码编译为 C++，执行效率更高
2. **启动优化**: 可以考虑使用 `--onefile` 创建单文件版本
3. **大小优化**: 使用 `--remove-output` 清理不必要的文件

## 注意事项

1. 构建时间较长，请耐心等待
2. 确保有足够的磁盘空间（至少 5GB）
3. 构建过程中不要关闭终端
4. 首次构建会下载必要的工具链
5. 建议在干净的环境中构建，避免依赖冲突
