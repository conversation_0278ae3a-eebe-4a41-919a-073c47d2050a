QTableView {
    background-color: white;
    border: none;
    border-radius: 8px;
    selection-background-color: rgba(61, 126, 255, 0.2);
}

QTableView::item {
    padding: 4px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

QTableView::item:selected {
    background-color: rgba(61, 126, 255, 0.1);
    color: #333333;
}

QHeaderView::section {
    background-color: #f8f9fa;
    padding: 8px;
    border: none;
    border-bottom: 2px solid #e9ecef;
    font-weight: bold;
    color: #495057;
}

QScrollBar:vertical {
    border: none;
    background: #f8f9fa;
    width: 12px;
    margin: 0px 0px 0px 0px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #dee2e6;
    min-height: 30px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background: #ced4da;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #f8f9fa;
    height: 12px;
    margin: 0px 0px 0px 0px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background: #dee2e6;
    min-width: 30px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background: #ced4da;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}