ComboBox {
    border: 1px solid rgba(0, 0, 0, 0.073);
    border-radius: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.183);
    padding: 0 30px 0 10px;  /* 调整内边距 */
    color: black;
    background-color: rgba(255, 255, 255, 0.7);
    text-align: left;
    outline: none;
    min-height: 27px;  /* 设置最小高度为27px */
    max-height: 27px;  /* 设置最大高度为27px */
}

ComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 25px;
    border-left-width: 0px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}