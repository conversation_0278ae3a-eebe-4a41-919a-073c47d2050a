#!/usr/bin/env python3
"""
最小化测试程序，用于验证 httpx 导入问题
"""

def main():
    print("=== 最小化 httpx 测试 ===")
    
    # 测试 httpx 导入
    try:
        import httpx
        print(f"✅ httpx 导入成功，版本: {httpx.__version__}")
        
        # 测试创建客户端
        client = httpx.Client()
        print("✅ httpx.Client 创建成功")
        client.close()
        print("✅ httpx.Client 关闭成功")
        
        print("🎉 httpx 测试完全成功！")
        return 0
        
    except ImportError as e:
        print(f"❌ httpx 导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ httpx 使用失败: {e}")
        return 1

if __name__ == "__main__":
    main()
