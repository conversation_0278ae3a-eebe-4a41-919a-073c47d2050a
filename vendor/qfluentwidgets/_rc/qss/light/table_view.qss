QTableView {
    background: transparent;
    outline: none;
    border: none;
    /* font: 13px 'Segoe UI', 'Microsoft YaHei'; */
    selection-background-color: transparent;
    alternate-background-color: transparent;
}

QTableView[isBorderVisible=true] {
    border: 1px solid rgba(0, 0, 0, 15);
}

QTableView::item {
    background: transparent;
    border: 0px;
    padding-left: 16px;
    padding-right: 16px;
    height: 35px;
}


QTableView::indicator {
    width: 18px;
    height: 18px;
    border-radius: 5px;
    border: none;
    background-color: transparent;
}


QHeaderView {
    background-color: transparent;
}

QHeaderView::section {
    background-color: transparent;
    color: rgb(96, 96, 96);
    padding-left: 5px;
    padding-right: 5px;
    border: 1px solid rgba(0, 0, 0, 15);
    font: 13px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
}

QHeaderView::section:horizontal {
    border-left: none;
    height: 33px;
}

QTableView[isBorderVisible=true] QHeaderView::section:horizontal {
    border-top: none;
}

QHeaderView::section:horizontal:last {
    border-right: none;
}

QHeaderView::section:vertical {
    border-top: none;
}

QHeaderView::section:checked {
    background-color: transparent;
}

QHeaderView::down-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Down_black.svg);
}

QHeaderView::up-arrow {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    margin-right: 6px;
    image: url(:/qfluentwidgets/images/table_view/Up_black.svg);
}

QTableCornerButton::section {
    background-color: transparent;
    border: 1px solid rgba(0, 0, 0, 15);
}

QTableCornerButton::section:pressed {
    background-color: rgba(0, 0, 0, 12);
}