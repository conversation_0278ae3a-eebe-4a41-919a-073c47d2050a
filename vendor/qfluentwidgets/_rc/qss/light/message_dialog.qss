QWidget {
    background-color: white;
    border: 1px solid rgb(200, 200, 200);
}

QWidget#windowMask {
    background-color: rgba(255, 255, 255, 0.6);
    border: none;
}

QLabel {
    background-color: transparent;
    color: black;
    font-family: 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    border: none;
}

QLabel#titleLabel {
    font-size: 18px;
}

QLabel#contentLabel {
    font-size: 14px;
}

QPushButton {
    background-color: rgb(204, 204, 204);
    font: 15px 'Segoe UI', 'Microsoft YaHei', 'PingFang SC';
    border: transparent;
}

QPushButton:pressed:hover {
    background-color: rgb(153, 153, 153);
}

QPushButton:hover {
    background-color: rgb(230, 230, 230);
}

QPushButton:disabled {
    background-color: rgb(204, 204, 204);
    color: rgb(122, 122, 122);
}