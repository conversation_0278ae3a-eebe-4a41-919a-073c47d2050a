SpinBox,
DoubleSpinBox,
DateEdit,
DateTimeEdit,
TimeEdit,
CompactSpinBox,
CompactDoubleSpinBox,
CompactDateEdit,
CompactDateTimeEdit,
CompactTimeEdit {
    background-color: rgba(255, 255, 255, 0.0605);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.5442);
    border-radius: 5px;
    /* font: 14px "Segoe UI", "Microsoft YaHei"; */
    padding: 0px 80px 0 10px;
    color: white;
    selection-background-color: --ThemeColorPrimary;
    selection-color: black;
}

CompactSpinBox,
CompactDoubleSpinBox,
CompactDateEdit,
CompactDateTimeEdit,
CompactTimeEdit {
    padding: 0px 26px 0 10px;
}

SpinBox:read-only,
DoubleSpinBox:read-only,
DateEdit:read-only,
DateTimeEdit:read-only,
TimeEdit:read-only,
CompactSpinBox:read-only,
CompactDoubleSpinBox:read-only,
CompactDateEdit:read-only,
CompactDateTimeEdit:read-only,
CompactTimeEdit:read-only,
SpinBox[symbolVisible=false],
DoubleSpinBox[symbolVisible=false],
DateEdit[symbolVisible=false],
DateTimeEdit[symbolVisible=false],
TimeEdit[symbolVisible=false],
CompactSpinBox[symbolVisible=false],
CompactDoubleSpinBox[symbolVisible=false],
CompactDateEdit[symbolVisible=false],
CompactDateTimeEdit[symbolVisible=false],
CompactTimeEdit[symbolVisible=false] {
    padding: 0px 10px 0 10px;
}

SpinBox:hover,
DoubleSpinBox:hover,
DateEdit:hover,
DateTimeEdit:hover,
TimeEdit:hover,
CompactSpinBox:hover,
CompactDoubleSpinBox:hover,
CompactDateEdit:hover,
CompactDateTimeEdit:hover,
CompactTimeEdit:hover {
    background-color: rgba(255, 255, 255, 0.0837);
}

SpinBox[transparent=true]:focus,
DoubleSpinBox[transparent=true]:focus,
DateEdit[transparent=true]:focus,
DateTimeEdit[transparent=true]:focus,
TimeEdit[transparent=true]:focus,
CompactSpinBox[transparent=true]:focus,
CompactDoubleSpinBox[transparent=true]:focus,
CompactDateEdit[transparent=true]:focus,
CompactDateTimeEdit[transparent=true]:focus,
CompactTimeEdit[transparent=true]:focus {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background-color: rgba(30, 30, 30, 0.7);
}

SpinBox:disabled,
DoubleSpinBox:disabled,
DateEdit:disabled,
DateTimeEdit:disabled,
TimeEdit:disabled,
CompactSpinBox:disabled,
CompactDoubleSpinBox:disabled,
CompactDateEdit:disabled,
CompactDateTimeEdit:disabled,
CompactTimeEdit:disabled {
    color: rgba(255, 255, 255, 92);
    background-color: rgba(255, 255, 255, 0.0837);
    border: 1px solid rgba(255, 255, 255, 0.0698);
}

SpinBox[transparent=false]:focus,
DoubleSpinBox[transparent=false]:focus,
DateEdit[transparent=false]:focus,
DateTimeEdit[transparent=false]:focus,
TimeEdit[transparent=false]:focus,
CompactSpinBox[transparent=false]:focus,
CompactDoubleSpinBox[transparent=false]:focus,
CompactDateEdit[transparent=false]:focus,
CompactDateTimeEdit[transparent=false]:focus,
CompactTimeEdit[transparent=false]:focus {
    background: rgb(31, 31, 31);
}

SpinButton {
    background-color: transparent;
    border-radius: 4px;
    margin: 0;
}

SpinButton:hover {
    background-color: rgba(255, 255, 255, 9);
}

SpinButton:pressed {
    background-color: rgba(255, 255, 255, 6);
}