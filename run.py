import sys

# 添加调试信息
print("🚀 程序启动中...")
print(f"Python 版本: {sys.version}")
print(f"Python 路径: {sys.executable}")

# 测试关键模块导入
try:
    import httpx
    print(f"✅ httpx 导入成功，版本: {httpx.__version__}")
except ImportError as e:
    print(f"❌ httpx 导入失败: {e}")
    print("这可能是打包配置问题")

try:
    from PySide6.QtWidgets import QApplication
    print("✅ PySide6 导入成功")
except ImportError as e:
    print(f"❌ PySide6 导入失败: {e}")

try:
    from nice_ui.ui.MainWindow import Window
    print("✅ MainWindow 导入成功")
except ImportError as e:
    print(f"❌ MainWindow 导入失败: {e}")

def main():
    print("🎯 创建 QApplication...")
    app = QApplication(sys.argv)

    print("🏠 创建主窗口...")
    window = Window()

    print("👁️ 显示窗口...")
    window.show()

    print("🔄 启动事件循环...")
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
