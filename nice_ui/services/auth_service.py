from typing import Optional, Dict, Any, Callable

from PySide6.QtCore import QSettings

from api_client import api_client
from nice_ui.interfaces.auth import AuthInterface
from nice_ui.interfaces.ui_manager import UIManagerInterface
from nice_ui.ui import SettingsManager
from utils import logger


class AuthService(AuthInterface):
    """认证服务实现类，处理登录状态检查和管理"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(AuthService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, ui_manager: UIManagerInterface):
        """
        初始化认证服务

        Args:
            ui_manager: UI管理器实例
        """
        # 避免重复初始化
        if getattr(self, '_initialized', False):
            return

        self.ui_manager = ui_manager
        self._settings = None
        self._initialized = True

    def _get_settings(self):
        """
        获取设置对象

        Returns:
            QSettings: 设置对象
        """
        if self._settings is None:
            self._settings = SettingsManager.get_instance()
        return self._settings

    def check_login_status(self) -> (bool, int):
        """
        检查用户是否已登录

        Returns:
            bool: True表示已登录，False表示未登录,int:用户余额
        """
        try:
            # 获取设置对象
            settings = self._get_settings()

            # 检查是否有token
            if not settings.value('token'):
                logger.info("未找到token，需要登录")
                self.show_login_dialog()
                return False, None

            # 尝试进行一个简单的API调用来验证token是否有效
            try:
                # 获取用户余额信息来验证token
                balance_data = api_client.get_balance_t()
                if 'data' in balance_data and 'balance' in balance_data['data']:
                    user_balance = int(balance_data['data']['balance'])
                    logger.info(f"用户当前代币余额: {user_balance}")
                    # 用户已登录，可以继续使用云引擎
                    logger.info("用户已登录，可以继续使用云引擎")
                    return True, user_balance
                else:
                    logger.warning(f"响应数据格式不正确: {balance_data}")
                    return False, None
            except Exception as e:
                logger.info(f"Token验证失败，需要重新登录 {str(e)}")
                self.show_login_dialog()
                return False, None

        except Exception as e:
            logger.error(f"检查登录状态时发生错误: {str(e)}")
            self.show_login_dialog()
            return False, None

    def show_login_dialog(self, callback: Optional[Callable[[Dict[str, Any]], None]] = None) -> None:
        """
        显示登录对话框

        Args:
            callback: 登录成功后的回调函数，接收用户信息作为参数
        """
        self.ui_manager.show_login_window(callback)

    def logout(self) -> None:
        """用户登出"""
        # 清除API客户端的token
        api_client.clear_token()

        # 清除设置中的token和相关信息
        settings = self._get_settings()
        settings.remove('token')
        settings.remove('refresh_token')
        settings.sync()

        logger.info("用户已登出")

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前登录用户信息

        Returns:
            Dict或None: 用户信息字典，未登录时返回None
        """
        if not self.check_login_status():
            return None

        # 获取设置对象
        settings = self._get_settings()

        # 从设置中获取用户信息
        email = settings.value('email', '已登录')

        # 尝试获取用户ID
        try:
            user_id = api_client.get_id()
        except Exception as e:
            logger.warning(f"获取用户ID失败: {str(e)}")
            user_id = settings.value('user_id', '')

        return {
            'user_id': user_id,
            'email': email
        }
