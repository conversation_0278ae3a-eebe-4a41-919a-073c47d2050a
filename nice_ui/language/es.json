{"translate_language": {"onlycnanden": "ChatTTS only support Chinese and English", "peiyindayu31": "The number of dubbing errors is greater than 1/3, please check the", "chaochu255": "The original video path and name is too long, please shorten the video name and move it to a shorter path to avoid subsequent errors.", "teshufuhao": "Please do not include any special symbols such as + & ? : | etc. in the path or name of the original video to avoid subsequent errors.", "notjson": "Return response is not valid json data", "fanyicuowu2": "The number of translation errors is more than half, please check", "azureinfo": "Did you set the speech resource key and region values", "yuchulichucuo": "Error in preprocessing stage", "shibiechucuo": "Error in speech recognition", "fanyichucuo": "Error in subtitle translation", "peiyinchucuo": "Error in dubbing", "hebingchucuo": "Error in combine", "freechatgpt_tips": "apiskey.top sponsored ChatGPT, free to use", "yidaorujigewenjian": "Imported subtitle files:", "dakaizimubaocunmulu": "Open directory where translation saved", "quanbuwanbi": "All translated.", "srtgeshierror": "Error while formatting subtitle, please check if the subtitle file conforms to the srt format", "endopendir": "Click to open the save directory when done", "xinshoumoshitips": "Select the video you want to translate and set the original language of the video and the language you want to translate to", "enmodelerror": "Models with the .en suffix can only be used if the original language is English", "openaimodelerror": "Models starting with distil-whisper cannot be selected when using openai models.", "qiegeshujuhaoshi": "If the video is very large, this step will be time-consuming, so please be patient.", "youtubehasdown": "The download has begun, please check the progress in the pop-up download interface, WARNING: message do not need to pay attention to, just ignore it!", "starting...": "Processing will start and progress will be shown later", "dubbing speed up": "Dub speed up", "video speed down": "Video slowdown", "auto_ajust": "Voice Extension", "auto_ajust_tooltips": "When this item is checked, if the voiceover is longer than the original duration, the immediately following mute clip will be taken up first in the backward direction\nIf there is still not enough time to fully place the voiceover, then the mute clip will be taken forward.\nAdjust according to whether the voiceover is automatically sped up or the video is automatically slowed down.", "tts tooltip": "Choose the dubbing channel, clone-voice/GPT-SoVITS must first fill in the API information in the upper-left menu - Settings.", "trans tooltip": "Select the translation channel to use, Google/Gemini/chatGPT official interface can not be connected to the domestic, you must fill in the right side of the network proxy address", "fenlinoviceerror": "Failed to separate novoice.mp4.", "No subtitles file": "No subtitle file", "Subtitles error": "Error formatting subtitle information", "get video_info error": "Error getting video information, please check if the video can be played.", "nogptsovitsurl": "You must fill in the api address of GPT-SoVITS", "nogptsovitslanguage": "GPT-SoVITS only supports Chinese, English and Japanese languages", "miandailigoogle": "Use Google translate when no proxy", "ttsapi_nourl": "The url of the customized TTS-API must be filled in before it can be used", "import src error": "Error importing subtitles, please check if srt subtitle format content exists in the file", "openaimodelnot": "You choose the openai {name} model, but the {name} model does not exist, please download and put the pt file in the models folder in the software directory, click on the menubar - Help Support - Download Model ", "recogn result is empty": "No subtitle was recognized, please check if it contains human voice and if the voice category matches the original {lang} language you chose, if both are normal, please select the 'Preserve background sound' option in the standard function mode and retry", "pianduan": " Partment ", "Download Models": "Download Models", "Start Separate": "Start separation", "Start Separate...": "Separating/click stop", "Separate End/Restart": "Separation completed/starting again", "must select audio or video file": "必须选择音视频文件", "zimusrterror": "The subtitle area already has subtitles that do not meet the SRT format requirements. Please delete all subtitle area text or import the correct SRT subtitle format again", "Export srt": "Export srt", "When subtitles exist, the subtitle content can be saved to a local SRT file": "When subtitles exist, the subtitle content can be saved to a local SRT file", "You must fill in the YouTube video playback page address": "You must fill in the YouTube video playback page address", "Error merging background and dubbing": "Error merging background and dubbing", "only10": "This software only supports Win10 and above systems on the Windows platform", "sp.exeerror": "sp.exe must be located in the original directory after decompression (i.e. in the same directory as videotrans|models|_internal), please do not copy or move it to any other location without permission", "meitiaozimugeshi": "Each subtitle format is as follows:\nLine number\nStart time hours:minutes:seconds,milliseconds -->End time hours:minutes:seconds,milliseconds\nSubtitle content", "Set up separate dubbing roles for each subtitle to be used": "Set up separate dubbing roles for each subtitle to be used", "daoruzimutips": "Existing SRT subtitle files can be imported locally, skipping the recognition and translation stages and using the imported subtitles directly", "Click to start the next step immediately": "Click to start the next step immediately", "Click to pause and modify subtitles for more accurate processing": "Click to pause and modify subtitles for more accurate processing", "zimubianjitishi": "During pause period, subtitles can be edited", "test google": "Test connection Google..", "Select Out Dir": "Select Out Dir", "downing...": "Downloading...", "start download": "Start Download", "Down done succeed": "Down done succeed", "videodown..": "Video is being slowed down", "Dubbing..": "Dubbing..", "Separating background music": "Separating background music", "Unable to connect to the API": "Unable to connect to the API", "bixutianxiecloneapi": "You must be input api fill in the address in the menu bar-Settings-cloneVoice ", "Save": "Save", "Open Documents": "Open Documents for Beginner", "Preserve the original sound in the video": "Preserve the original sound in the video", "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices": "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices", "lanjie": "Active restriction", "The ott project at": "The OTT text translate at: github.com/jianchang512/ott", "No select videos": "No select videos", "You must deploy and start the clone-voice service": "You must deploy and start the github.com/jianchang512/clone-voice  service, and fill in the address in the menu bar-Settings-cloneVoice", "cannot connection to clone-voice service": "Cannot connection to clone-voice service,You must deploy and start the github.com/jianchang512/clone-voice  service, and fill in the address in the menu bar-Settings-cloneVoice", "test clone voice": "test connecting to clone-voice api ...", "The project at": "<PERSON><PERSON> voice at: github.com/jianchang512/clone-voice", "qianyiwenjian": "La vía de acceso o nombre de vídeo contiene espacios no ASCII.  Para evitar errores, se ha migrado a   ", "bukebaoliubeijing": "Must have a voice actor and input video to preserve background music", "Separating vocals and background music, which may take a longer time": "Separating vocals and background music, which may take a longer time", "mansuchucuo": "Vídeo automático de error lento, por favor, intente cancelar la opción 'Video auto down' ", "zimuwenjianbuzhengque": "Error de archivo de subtítulos, el tamaño es 0b ", "huituicpu": "Error de ejecución en la GPU, retrotracción a la ejecución de la CPU ", "zimuhangshu": "subtítulos Line   ", "kaishihebing": "Iniciar la fusión y salida de archivos de resultados ", "kaishishibie": "Iniciar el reconocimiento de voz ", "kaishitiquyinpin": "Iniciar la extracción de audio ", "kaishiyuchuli": "Iniciar el preproceso de vídeo en formato estándar ", "fengeyinpinshuju": "Dividir datos antes del reconocimiento de voz ", "yuyinshibiejindu": "Progreso del reconocimiento de voz ", "yuyinshibiewancheng": "Reconocimiento de voz completado ", "shipinmoweiyanchang": "Ampliar el extremo del vídeo ", "shipinjiangsu": "Ampliar el vídeo ", "xinshipinchangdu": "Nueva longitud de vídeo después de ralentizarse ", "peiyin-yingzimu": "Síntesis del subtítulo de voz en off + ", "peiyin-ruanzimu": "Síntesis de voz en off + subtítulo suave ", "onlypeiyin": "Incrustación de voz, sin subtítulos ", "onlyyingzimu": "Incrustación de subtítulos duros, sin voz ", "onlyruanzimu": "Incrustación de subtítulos suaves, sin voz en off ", "tuodonghuoshuru": "Escriba el texto o arrastre el archivo SRT del subtítulo aquí ", "ffmpegerror": "Compruebe si la configuración de CUDA es correcta o si el vídeo es un archivo mp4 codificado con H264 ", "sjselectmp4": "Haga doble clic para seleccionar vídeo o arrastrar vídeo aquí ", "default": "<PERSON><PERSON> predeterminado ", "zhishaoxuanzeyihang": "Debe seleccionarse al menos una fila ", "continue_action": "Ir al siguiente paso ", "xuanzejuese": "debe seleccionar el rol de doblaje, el idioma del subtítulo de selección de frist y seleccionar el rol de doblaje ", "tencent_key": "debe ser SecretId de tencent y SecretKey de entrada ", "qingqueren": "Confirme por favor ", "only_srt": "No seleccione el idioma de destino, s<PERSON>lo creará el archivo srt, pulse Sí para continuar, de lo contrario, cancele ", "mustberole": "debe ser el rol selet para la escucha ", "setdeepl_authkey": "debe establecerse en la señal DeepL ", "setdeeplx_address": "debe establecer la dirección y el puerto de DeepLX ", "setott_address": "must be set OTT address and port", "subtitle_tips": " Editar subtítulo aquí o drop srt file to here   ", "waitclear": "proceso de cierre ", "whisper_type_all": "Overall", "whisper_type_split": "Pre-split", "whisper_type_avg": "Equal-division", "fenge_tips": "Overall: The model automatically breaks sentences for the whole audio.\nPre-split: suitable for very large videos, cut into 1-minute clips to recognize and break sentences one by one.\nEqual-division: cut equally according to a fixed number of seconds, each subtitle is of equal length, the length is controlled by interval_split in set.ini.", "processingstatusbar": "Vídeo de proceso: [{var1}] total, [{var2}] waitting ", "yinsekelong": "La clonación de Timbre utilizará github.com/jianchang512/clone-voice.  Estas características se utilizarán entonces como la voz para los personajes de doblaje, logrando el doblaje personalizado con cualquier timbre deseado. ", "yinsekaifazhong": "La clonación de Timbre está en desarrollo. ", "installffmpeg": "No ffmpeg, por favor vaya a ffmpeg.org para descargar y colocar el archivo ffmpeg y ffprobe bajo la raíz de este software ", "hechengchucuo": "Error de composición de vídeo, archivo perdido: ", "queding": "Confirmar ", "wait_edit_subtitle": "Esperar el subtítulo de edición ", "autocomposing": " segundos Después de la cuenta atrás, el vídeo se sintetiza automáticamente.  haga clic en Pausa, la cuenta regresiva ", "deepl_nosupport": "No apoye la traducción al idioma ", "deepl_authkey": "Necesita una clave de autenticación DeepL ", "confirmstop": "¿Detener esta tarea? ", "prcoessingstatusbar": "Procesando vídeo: [{var1}], con [{var2}] a la espera de ser procesado ", "createdirerror": "crear error dir ", "waitforend": "Composición de vídeo ", "waitsubtitle": "Subtítulo de edición de espera (pulse para continuar) ", "baikeymust": "entrada de la clave de baidu ", "chatgptkeymust": "entrada de la clave de chatgpt ", "nosubtitle": "No Subtítulo ", "embedsubtitle": "Subtítulo de inclusión ", "softsubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> blando ", "embedsubtitle2": "Embed subtitle(double)", "softsubtitle2": "Soft subtitle(double)", "modellost": "Hay un error en la descarga del modelo o la descarga es incompleta.  Vuelva a descargarlo y almacenarlo en el directorio de modelos. ", "modelpathis": "Ruta de almacenamiento del modelo: ", "downloadmodel": "The {name} model does not exist. click on the menubar - Help Support - Download Model ", "waitrole": "obtener lista de roles de voz, mantener ", "selectsavedir": "seleccionar un directorio para la salida ", "selectmp4": "seleccionar un vídeo mp4 ", "subtitleandvoice_role": "no hay vídeo y tiene subtítulo en el área de edición, creará archivos de audio de doblaje wav, confirmar para continuar? ", "proxyerrortitle": "Error de proxy ", "shoundselecttargetlanguage": "Debe seleccionar un idioma de destino   ", "proxyerrorbody": "Error al acceder a los servicios de Google.  Configure el proxy correctamente. ", "softname": "Traducción de vídeo y Dubbing ", "anerror": "Se ha producido un error ", "selectvideodir": "Debe seleccionar el vídeo que desea traducir ", "sourenotequaltarget": "El idioma de origen y el idioma de destino no deben ser los mismos ", "running": "Ejecutando ... ", "ing": "Ejecutando ... ", "exit": "Salida ", "end": "Finalizado (pulse en reinicio) ", "stop": "Detener (pulse en reinicio) ", "error": "Se ha producido un error (pulse en reinicio) ", "daoruzimu": "Importar subtítulos ", "shitingpeiyin": "Doblaje experimental ", "xianqidongrenwu": "En primer lugar, iniciar la tarea, se puede escuchar después de que se completa la traducción del subtítulo, la velocidad de doblaje y la aceleración automática entran en vigor en tiempo real ", "juanzhu": " Por favor, considere donar al software para mantenerlo actualizado y mantenido.   ", "nocuda": "El dispositivo no cumple los requisitos de aceleración de CUDA, por favor, confirme que es una tarjeta gráfica NVIDIA y que el entorno CUDA se ha configurado, puede ir a la página de descripción del repositorio para ver el método de instalación y, click menubar-Help&support-CUDA help", "cudatips": "Enable if you have an NVIDIA graphics card and configured with a CUDA environment, will greatly improve execution speed", "nocudnn": "Your device does not have cuDNN installed and configured, please refer to the https://juejin.cn/post/7318704408727519270#heading-1 Install and then restart the software or use openai model", "noselectrole": "No se ha seleccionado ningún rol, no se puede probar ", "chongtingzhong": "Volver a escuchar ", "shitingzhong": "Escuchar/pulsar para volver a escuchar ", "bukeshiting": "No hay contenido de subtítulo, no se puede probar ", "tiquzimu": "Establezca el idioma original en el idioma expresado en el vídeo, el idioma de destino es el idioma al que desea traducir ", "kaishitiquhefanyi": "Empezar a extraer y traducir ", "tiquzimuno": "Establezca el idioma original en el idioma expresado en el vídeo ", "kaishitiquzimu": "Iniciar la extracción de subtítulos ", "zimu_video": "Seleccione el vídeo que desea fusionar, arrastre y suelte el archivo de subtítulos en el área de subtítulo derecho ", "zimu_peiyin": "Establezca el idioma de destino como idioma utilizado para los subtítulos y seleccione el rol de doblaje ", "kaishipeiyin": "Iniciar voiceover ", "anzhuangvlc": "Es posible que necesite instalar primero el decodificador VLC, ", "jixuzhong": "<PERSON><PERSON><PERSON><PERSON> eje<PERSON>o ", "nextstep": "Pasar al paso siguiente ", "tianxieazure": "Debe rellenar la clave Azure ", "bencijieshu": "Esta tarea ha terminado ", "kaishichuli": "Iniciar proceso", "yunxingbukerole": "<PERSON><PERSON><PERSON><PERSON>, no puede cambiar a sin rol de doblaje ", "bixutianxie": "<PERSON><PERSON> re<PERSON>ar el ", "peiyinmoshisrt": "Debe elegir un rol de doblaje, el idioma de destino en modalidad de doblaje y arrastrar y soltar el archivo de subtítulo de srt local en el área de subtítulo derecho ", "hebingmoshisrt": "En la modalidad de fusión, debe seleccionar un vídeo, un tipo de incrustación de subtítulos y arrastrar y soltar el archivo srt de subtítulos en el área de subtítulo derecho ", "fanyimoshi1": "Debe elegir el idioma de destino para traducir a ", "bukedoubucunzai": "¡Los vídeos y los subtítulos no pueden ser inexistentes al mismo tiempo! ", "wufapeiyin": "Ningún idioma de destino seleccionado, no puede doblarse, seleccione el idioma de destino o cancele el rol de doblaje ", "xuanzeyinpinwenjian": "Seleccione archivos de audio y vídeo ", "vlctips": "Arrastre el vídeo aquí o efectúe una doble pulsación para seleccionar el vídeo ", "vlctips2": " file is not exists ", "xuanzeyinshipin": "Haga clic para seleccionar o arrastrar y soltar archivos de audio y vídeo aquí ", "tuodongdaoci": "Arrastre el archivo que desea convertir aquí y libere ", "tuodongfanyi": "import subtitles file ", "zhixingwc": "Ejecución completada ", "zhixinger": "Error de ejecución ", "starttrans": "Empezar a traducir ", "yinpinhezimu": "Se debe seleccionar al menos uno de audio y subtítulos ", "bixuyinshipin": "Debe seleccionar un archivo de audio y vídeo válido ", "chakanerror": "El proceso previo ha fallado antes del reconocimiento, confirme si hay datos de audio en el vídeo ", "srtisempty": "El contenido del subtítulo es ", "savesrtto": "Elija guardar el archivo de subtítulo en .. ", "neirongweikong": "El contenido no puede estar vacío ", "yuyanjuesebixuan": "Se debe seleccionar el idioma y el rol ", "nojueselist": "No se ha podido obtener la lista de roles ", "buzhichijuese": "Este rol de voz no está soportado ", "nowenjian": "No existen archivos válidos ", "yinpinbuke": "El audio no se puede convertir a ", "quanbuend": "Todas las conversiones se realizan ", "wenbenbukeweikong": "El texto que se va a traducir no puede estar vacío ", "buzhichifanyi": "No da soporte a la conversión al idioma de destino ", "ffmpegno": "ffmpeg no encontrado, el software no está disponible, por favor descargue de ffmpeg.org y añada a las variables de entorno del sistema ", "newversion": "Hay una nueva versión para descargar ", "tingzhile": "Detenido ", "geshihuazimuchucuo": "Error al formatear el archivo de subtítulo ", "moweiyanchangshibai": "No se han podido añadir tramas de vídeo de extensión al final, se mantendrá el estado original sin ampliar el vídeo ", "xiugaiyuanyuyan": "Espere a modificar los subtítulos de idioma original/continúe ", "jimiaohoufanyi": "Traducido automáticamente en segundos, puede hacer clic en Pausar y editar subtítulos para que la traducción sea más precisa ", "xiugaipeiyinzimu": "Espere a modificar los subtítulos doblados/pulse continuar ", "zidonghebingmiaohou": "Fusionar automáticamente en segundos, puede hacer clic en Pausa para modificar los subtítulos para que el doblaje sea más preciso ", "jieshutips": "Termina el procesamiento de video: Se pueden encontrar materiales relevantes en la carpeta de destino, incluyendo archivos de subtítulos, archivos de voz en off, etc. ", "mubiao": "<PERSON><PERSON><PERSON> de salida ", "endandopen": "Finalizado el clic para abrir   ", "waitforstart": "Espere a que se inicie ", "xianxuanjuese": "Seleccione el tipo de TTS y el rol primero ", "shezhijueseline": "Rellene el número de líneas utilizando la voz del personaje actuando, por ejemplo.  2,3,7,8 ", "youzimuyouset": "Los roles múltiples sólo se pueden establecer por línea después de mostrar los subtítulos completos en el área de subtítulo ", "shezhijuese": "Establecer rol "}, "ui_lang": {"action_xinshoujandan": "Minimalist Newbie Mode", "action_xinshoujandan_tools": "Simple to use without configuration, suitable for short videos, less accurate, for more control use standard function mode", "onlyvideo": "Save only video", "onlyvideo_tips": "If this item is checked, subtitles, audio and other material will not be kept, only the final video file will be saved", "model_type_tips": "The master model is faster and more resource-efficient, but requires the installation and configuration of cudnn and cublas in addition to cuda.\nThe openai model is slower and more resource-intensive, but only requires cuda to be installed.", "faster model": "faster model", "openai model": "openai model", "addbackbtn": "Add background music", "back_audio_place": "The full path of the background music, deleting it does not add it", "Download Models": "Download Models", "Download cuBLASxx.dll": "Download cuBLASxx.dll", "Separate vocal voice": "Separate vocal voice", "SP-video Translate Dubbing": "SP-vídeo Translate doblaje ", "Multiple MP4 videos can be selected and automatically queued for processing": "Se pueden seleccionar varios vídeos MP4 y se pueden poner en cola automáticamente para su proceso ", "Select video..": "Seleccione el vídeo. ", "Select where to save the processed output resources": "Seleccione dónde guardar los recursos de salida procesados ", "Save to..": "Guardar en .. ", "Open target dir": "Abrir directorio de destino ", "Open": "<PERSON>bie<PERSON>o ", "Translate channel": "Canal de conversión ", "Proxy": "Representante ", "proxy address": "dirección proxy ", "shuoming01": "Haga clic para escuchar la pronunciación del carácter de doblaje actual, la generación de doblaje puede tardar unos segundos, por favor sea paciente y espere ", "Trial dubbing": "Doblaje experimental ", "Source lang": "<PERSON><PERSON> lang ", "The language used for the original video pronunciation": "El idioma utilizado para la pronunciación de vídeo original ", "Target lang": "Objetivo lang ", "What language do you want to translate into": "¿En qué idioma quieres traducir? ", "Dubbing role": "Función doblaje ", "No is not dubbing": "No es el doblaje ", "From base to large v3, the effect is getting better and better, but the speed is also getting slower and slower": "Desde la base hasta la gran v3, el efecto es cada vez mejor, pero la velocidad también es cada vez más lenta y lenta. ", "Overall recognition is suitable for videos with or without background music and noticeable silence": "El reconocimiento general es adecuado para videos con o sin música de fondo y un silencio notable ", "Embed subtitles": "Incluir subtítulos ", "shuoming02": "Los subtítulos incrustados siempre muestran subtítulos sin importar dónde se juegan y no pueden ser ocultados. Si es apoyado por el jugador, los subtítulos suaves pueden ser controlados para ser mostrados o ocultos en el reproductor. \n, Si desea mostrar subtítulos al jugar en una página web, por favor seleccione subtítulos incrustados. ", "Silent duration": "Duración silenciosa ", "default 500ms": "500ms predeterminados ", "Mute duration for segmented speech, in milliseconds": "Duración de silencio para el discurso segmentado, en milisegundos ", "Dubbing speed": "Velocidad do<PERSON> ", "Overall acceleration or deceleration of voice over playback": "Aceleración general o desaceleración de la voz sobre la reproducción ", "Positive numbers accelerate, negative numbers decelerate, -90 to+90": "Los números positivos se aceleran, los números negativos se desaceleran, -90 a + 90 ", "shuoming03": "After the translation of different languages under the pronunciation of different lengths, there is bound to be alignment problems, through the dubbing of the overall speed of speech, the dubbing of the automatic acceleration, the voice before and after the extension can be slightly alleviated, more methods and principles, please check the lower left corner of the relevant tutorials", "Voice acceleration?": "¿aceleración de voz? ", "shuoming04": "La duración de la pronunciación varía en diferentes idiomas después de la traducción, por ejemplo, si una frase está en chino durante 3 segundos, puede tardar 5 segundos en traducirlo al inglés, lo que resulta en una incoherencia entre la duración y el vídeo. \nDos soluciones: \n1. Fuerza de voz para acelerar la reproducción, con el fin de acortar la duración de la voz sobre y alinear con el vídeo \n2. Forzar el video para jugar lentamente con el fin de extender la duración del video y alinear la voz sobre. \nElija sólo una de las dos opciones ", "Video slow": "<PERSON><PERSON><PERSON><PERSON> lento ", "shuoming05": "Es necesario asegurarse de que hay una tarjeta gráfica NVIDIA y de que el entorno CUDA está correctamente configurado, no elegir ", "Enable CUDA?": "¿Habilitar CUDA? ", "Preserve background music": "Preserve BackMusic", "If retained, the required time may be longer, please be patient and wait": "If retained, the required time may be longer, please be patient and wait", "Start": "<PERSON><PERSON>o ", "Pause": "Pausa ", "Import srt": "Importar srt ", "Train voice": "Voz del tren ", "Set role by line": "Establecer rol por línea ", "&Setting": "&Establecer ", "&Tools": "&Herramientas ", "&Help": "&<PERSON><PERSON><PERSON> ", "toolBar": "toolBar ", "Video Toolbox": "Caja de herramientas ", "Go VLC Website": "Sitio web de VLC ", "FFmpeg": "FFmpeg ", "Go FFmpeg website": "Página web de FFmpeg ", "Post issue": "Post-edición ", "Clone Voice": "Clonar voz ", "Documents": "Documentos ", "Donating developers": "Donación de desarrolladores ", "Standard Function Mode": "Modalidad de función estándar ", "Display all options for video translation and dubbing": "Mostrar todas las opciones de conversión de vídeo y doblaje ", "Export  Srt  From Videos": "Exportar Srt De Vídeos ", "Extracting SRT subtitles in the original language from local videos": "Extracción de subtítulos de SRT en el idioma original de los vídeos locales ", "Merging Subtitle  Video": "Fusión de vídeo de subtítulo ", "Embed locally existing SRT subtitles into the video": "Embed los subtítulos de SRT existentes localmente en el vídeo ", "Subtitle Create Dubbing": "<PERSON><PERSON><PERSON> do<PERSON> de subtítulo ", "Local existing SRT subtitle generation dubbing WAV files": "Archivos WAV de doblaje de generación de subtítulo de SRT local existente ", "Speech Recognition Text": "Texto de reconocimiento de voz ", "Recognize the sound in audio or video and output SRT text": "Reconocer el sonido en audio o vídeo y salida de texto SRT ", "From  Text  Into  Speech": "Del texto al discurso ", "Generate audio WAV from text or SRT subtitle files": "Generar WAV de audio a partir de archivos de subtítulo de texto o SRT ", "Extract Srt And Translate": "Extraer Srt Y Traducir ", "Extract SRT subtitles from local videos in the original language and translate them into SRT subtitle files in the target language": "Extraer los subtítulos de SRT de los vídeos locales en el idioma original y traducirlos en archivos de subtítulo de SRT en el idioma de destino ", "Separate Video to audio": "Vídeo separado a audio ", "Separate audio and silent videos from videos": "Separe los vídeos de audio y silencioso de los vídeos ", "Video Subtitles Merging": "Fusión de subtítulos en vídeo ", "Merge audio, video, and subtitles into one file": "Fusionar audio, vídeo y subtítulos en un archivo ", "Files Format Conversion": "Conversión de formato de archivos ", "Convert various formats to each other": "Convertir varios formatos entre sí ", "Mixing 2 Audio Streams": "Mezcla de 2 flujos de audio ", "Mix two audio files into one audio file": "Mezclar dos archivos de audio en un archivo de audio ", "Text  Or Srt  Translation": "Batch translation subtitles", "Translate text or subtitles": "Traducir texto o subtítulos ", "Download from Youtube": "<PERSON><PERSON><PERSON> desde Youtube ", "Whisper model": "<PERSON><PERSON>rro "}, "toolbox_lang": {"Video Toolbox": "Caja de herramientas ", "import audio or video": "Import audio or video", "Start": "<PERSON><PERSON>o ", "No voice video": "No hay vídeo de voz ", "Open dir": "<PERSON>r abierto ", "Audio Wav": "Audio Wav ", "Video audio separation": "Separación de audio de vídeo ", "Video file": "Archivo de vídeo ", "Select video": "Seleccionar vídeo ", "Audio file": "Archivo de audio ", "Select audio": "Seleccionar audio ", "Subtitle srt": "Subtitle srt ", "Select srt file": "Seleccionar archivo srt ", "Open output dir": "Abrir directorio de salida ", "Video subtitle merging": "Fusión de subtítulo de vídeo ", "Source lang": "<PERSON><PERSON> lang ", "Whisper model": "<PERSON><PERSON>rro ", "Save to srt..": "Guardar en srt .. ", "Voice recognition": "Reconocimiento de voz ", "Subtitle lang": "<PERSON><PERSON><PERSON><PERSON><PERSON> lang ", "Select role": "Seleccionar rol ", "Speed change": "Cambio de velocidad ", "Negative deceleration, positive acceleration": "Desaceleración negativa, aceleración positiva ", "If so, the line number and time value will skip reading aloud": "Si es así, el número de línea y el valor de hora omitirán la lectura en voz alta ", "Is srt?": "¿Es srt? ", "Automatic acceleration?": "¿Aceleración automática? ", "Output audio name": "Nombre de audio de salida ", "Set the name of the generated audio file here. If not filled in, use the time and date command": "Establezca aquí el nombre del archivo de audio generado.  Si no se rellena, utilice el mandato de fecha y hora ", "Text to speech": "Texto al discurso ", "Convert mp4->": "Convertir mp4-> ", "Convert avi->": "Convertir avi-> ", "Convert mov->": "Convertir mov-> ", "Convert wav->": "Convertir wav-> ", "Convert mp3->": "Convertir mp3-> ", "Convert aac->": "Convertir aac-> ", "Convert m4a->": "Convertir m4a-> ", "Conver flac->": "Conver flac-> ", "The conversion result is displayed here": "El resultado de la conversión se visualiza aquí ", "Audio and video format conversion": "Conversión de audio y formato de vídeo ", "Audio file 1": "Archivo de audio 1 ", "Select the first audio file": "Seleccione el primer archivo de audio ", "Audio file 2": "Archivo de audio 2 ", "Select the second audio file": "Seleccione el segundo archivo de audio ", "You can customize the output file name here. If not filled in, use a date name": "Puede personalizar el nombre del archivo de salida aquí.  Si no se rellena, utilice un nombre de fecha ", "Mixing two audio streams": "Cómo mezclar dos corrientes de audio ", "Translation channels": "Canales de traducción ", "Target lang": "Objetivo lang ", "Proxy": "Representante ", "Failed to access Google services. Please set up the proxy correctly": "Configure el proxy correctamente ", "Import text to be translated from a file..": "Importar texto que se va a traducir de un archivo. ", "shuoming1": "Only srt format subtitle files are allowed to be translated, please do not import and translate files that do not symbolize this format, otherwise an error will be reported. ", "export..": "export .. ", "Start>": "Inicio > ", "The translation result is displayed here": "El resultado de la traducción se muestra aquí ", "Text subtitle translation": "Traducción del subtítulo de texto "}, "language_code_list": {"zh-cn": "Chino simplificado ", "zh-tw": "Chino tradicional ", "en": "Inglés ", "fr": "<PERSON><PERSON><PERSON><PERSON> ", "de": "Alemán ", "ja": "Japonés ", "ko": "<PERSON><PERSON> ", "ru": "<PERSON><PERSON><PERSON> ", "es": "Español ", "th": "Tailandia ", "it": "Italiano ", "pt": "Portugués ", "vi": "Vietnamita ", "ar": "<PERSON><PERSON><PERSON> ", "tr": "Turquía ", "hi": "Hindi", "hu": "Hungarian", "uk": "Ukrainian", "id": "Indonesian", "ms": "Malay", "kk": "Kazakh", "cs": "Czech"}}